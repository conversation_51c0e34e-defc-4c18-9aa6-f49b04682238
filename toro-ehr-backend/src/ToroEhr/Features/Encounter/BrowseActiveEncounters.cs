using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Indexes;

namespace ToroEhr.Features.Encounter;

public record BrowseActiveEncountersQuery(PagedSearchParams PagedSearchParams, bool ShowAll)
    : AuthRequest<PaginatedList<ActiveEncounterResponse>>;

public record ActiveEncounterResponse(
    string Id,
    string PatientId,
    string PatientName,
    string PractitionerId,
    string PractitionerName,
    DateTimeOffset StartAt,
    EncounterStatus Status,
    bool HasNotes);

internal class BrowseActiveEncountersAuth : IAuth<BrowseActiveEncountersQuery, PaginatedList<ActiveEncounterResponse>>
{
    public BrowseActiveEncountersAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class
    BrowseActiveEncountersHandler : IRequestHandler<BrowseActiveEncountersQuery, PaginatedList<ActiveEncounterResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public BrowseActiveEncountersHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<ActiveEncounterResponse>> Handle(BrowseActiveEncountersQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Encounters_Comprehensive.Entry> dbQuery = session
            .Query<Encounters_Comprehensive.Entry, Encounters_Comprehensive>()
            .ProjectInto<Encounters_Comprehensive.Entry>()
            .Where(x => x.LocationId == _user.SelectedLocationId &&
                       (x.StartAtDate == query.Timestamp.Date || x.EncounterStatus == EncounterStatus.InProgress.Name));

        if (query.ShowAll == false)
        {
            dbQuery = dbQuery.Where(x => x.PractitionerId == _user.EmployeeId);
        }

        var encounterEntries = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<ActiveEncounterResponse>.Create(
            encounterEntries
                .Select(x => new ActiveEncounterResponse(
                    x.EncounterId,
                    x.PatientId,
                    x.PatientFullName,
                    x.PractitionerId,
                    x.PractitionerFullName,
                    x.StartAt,
                    EncounterStatus.FromName(x.EncounterStatus),
                    x.NoteIds.Any())).ToList(),
            stats.TotalResults,
            query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize
        );
    }
}