using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Medications_Search : AbstractIndexCreationTask<Medication, Medications_Search.Entry>
{
    public class Entry
    {
        public string MedicationId { get; set; } = null!;
        public string SourceAbbreviation { get; set; } = null!;
        public string[] Search { get; set; } = null!;
    }

    public Medications_Search()
    {
        Map = medications =>
            from medication in medications
            select new Entry
            {
                MedicationId = medication.Id,
                SourceAbbreviation = medication.SourceAbbreviation,
                Search = new[]
                {
                    medication.SourceCode,
                    medication.TermString.Substring(0, 30)
                }
            };

        Stores.Add(x => x.MedicationId, FieldStorage.Yes);
        Stores.Add(x => x.SourceAbbreviation, FieldStorage.Yes);
        Analyzers.Add(n => n.Search, "NGramAnalyzer");
    }
}
