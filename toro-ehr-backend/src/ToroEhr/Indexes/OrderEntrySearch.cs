using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class OrderEntrySearch : AbstractMultiMapIndexCreationTask<OrderEntrySearch.Entry>
{
    public class Entry
    {
        public string Type { get; set; } = null!;
        public string Id { get; set; } = null!;
        public string Code { get; set; } = null!;
        public string DisplayName { get; set; } = null!;
        public string[] SearchParams { get; set; } = null!;
    }

    public OrderEntrySearch()
    {
        AddMap<Medication>(medications =>
            from medication in medications
            where medication.SourceAbbreviation == "RXNORM" &&
                  (medication.TermType == "IN" || medication.TermType == "MIN")
            select new Entry
            {
                Type = "Med",
                Id = medication.Id,
                Code = medication.RxCui,
                DisplayName = medication.TermString,
                SearchParams = new[]
                {
                    medication.RxCui,
                    medication.TermString.Length > 200 
                        ? medication.TermString.Substring(0, 200) 
                        : medication.TermString
                }
            });
        AddMap<LoincCode>(loincCodes =>
            from loincCode in loincCodes
            select new Entry
            {
                Type = "Lab",
                Id = loincCode.Id,
                Code = loincCode.LoincNum ?? string.Empty,
                DisplayName = loincCode.DisplayName ?? string.Empty,
                SearchParams = new[]
                {
                    loincCode.Id,
                    loincCode.DisplayName ?? string.Empty,
                }
            });
        AddMap<OrderBundleTemplate>(bundles =>
            from bundle in bundles
            select new Entry
            {
                Type = "Bundle",
                Id = bundle.Id,
                Code = string.Empty,
                DisplayName = bundle.Name,
                SearchParams = new[]
                {
                    bundle.Name,
                }
            });
        AddMap<SnomedCode>(snomedCodes =>
            from snomedCode in snomedCodes
            where snomedCode.Term != null && snomedCode.IsActive && snomedCode.IsProcedure
            select new Entry
            {
                Type = "Procedure",
                Id = snomedCode.Id,
                Code = snomedCode.SnomedId,
                DisplayName = $"SNOMED-{snomedCode.Term}",
                SearchParams = new[]
                {
                    "SNOMED",
                    snomedCode.Term,
                }
            });
        AddMap<CptCode>(cptCodes =>
            from cptCode in cptCodes
            where cptCode.Code != null
                  && cptCode.Code.Length == 5
                  && string.Compare(cptCode.Code, "00100") >= 0
                  && string.Compare(cptCode.Code, "69990") <= 0
            select new Entry
            {
                Type = "Procedure",
                Id = cptCode.Id,
                Code = cptCode.Code,
                DisplayName = $"CPT-{cptCode.ClinicianDescriptor}",
                SearchParams = new[]
                {
                    "CPT",
                    cptCode.Code,
                    cptCode.ClinicianDescriptor,
                }
            });

        Stores.Add(x => x.Type, FieldStorage.Yes);
        Stores.Add(x => x.Id, FieldStorage.Yes);
        Stores.Add(x => x.Code, FieldStorage.Yes);
        Stores.Add(x => x.DisplayName, FieldStorage.Yes);
        Analyzers.Add(n => n.SearchParams, "NGramAnalyzer");
    }
}